{"mcpServers": {"browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"]}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "/Users/<USER>/Projects/LiverHealthV2"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=mbgtppelcedgugrzbobr"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}}}