---
description:
globs:
alwaysApply: false
---
---
description: when user mentions /task, apply this rule
globs:
alwaysApply: false
---
## Rule to plan and execute tasks

# Systematic Task Executor

Act as a developer to execute tasks with two-phase planning and implementation approach with absolute precision and adherences to coding standards

## Usage
/task - Automatically selects task or picks the user guided task ID from `docs/tasks.json` (create if file not found) and prepares to plan and execute it in context of the `docs/prd.md`

## Command Implementation

Address user as **SIR-ENGINEER** throughout execution and execute the two phase protocol; declare the phase to the user and summarize each phase in a crisp manner to the user;

### PLANNING PHASE

**PLANNING PHASE** - Task [ID]: [Title]

**Systematic Context Gathering:**
<thinking>
Starting with uncertainty about implementation:
- What is the current state of files I need to modify?
- What dependencies exist and are they ready?
- What implementation approach will work best?

Building confidence through analysis:
- Reading the specific files mentioned in task
- Understanding existing patterns and conventions
- Identifying the precise changes needed
</thinking>

**Required File Analysis:**
- Read all files from task's `files_affected.modify` array
- Read all files from task's `files_affected.read` array for context
- Check `.docs/active-context.md` for recent changes and decisions
- Analyze existing patterns, naming conventions, architectures
- Verify all dependency tasks marked "done" in `tasks.json`

**Implementation Plan Development:**

Current State Analysis:

- File: [filename] - Current implementation and required changes
- Function: [function_name] - Existing signature and modifications needed
- Dependencies: [task_ids] - Prerequisite completion status
- Patterns: [conventions] - Existing code patterns to follow

**Technical Precision:**

- Exact file modifications with line-level targeting
- Specific function signatures and implementations
- Library imports and version requirements
- Integration with existing architecture patterns
- Error handling and edge case considerations

**Confidence Assessment**: overall confidence > 95% prior to implementation

**Approval Protocol:**
"Analysis complete with [confidence]% confidence. Ready to implement with systematic approach."

**CRITICAL**:
- New libraries require proper reasoning before introduction
- No implementation without explicit user approval

### EXECUTION PHASE (Only After Explicit Approval)

**Pre-Implementation Setup:**
- Update task status to "in-progress" in `docs/tasks.json`
- Implement the task as per the approved plan, adhering to project conventions and tech stack
- If errors occur, notify the user of the root cause and proposed fix; only proceed after user confirmation

**Implementation Standards:**
- Follow existing codebase patterns and conventions exactly
- Make minimal changes to achieve task requirements
- Write self-documenting code with clear intent
- Add meaningful comments for complex logic or decisions
- Maintain backward compatibility unless explicitly changing interfaces

**Task Completion Protocol:**
- **Critical** - notify user of task completion and seek explicit approval
- Mark task status "done" in `docs/tasks.json` with completion timestamp
- Update `docs/execution.md` with implementation notes
- Identify newly unblocked tasks based on dependency completion
- Present completion summary with files changed and next task recommendation

## Quality Standards
- 95%+ confidence maintained throughout implementation
- Code follows existing project patterns exactly
- Documentation reflects all changes made
