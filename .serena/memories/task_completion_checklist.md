# Task Completion Checklist

## Code Quality Checks

1. **Linting**: Run `bun run lint` to check for code issues
2. **Formatting**: Run `bun run format` to ensure consistent formatting
3. **Type Checking**: Run `bun run type-check` to verify TypeScript types
4. **Testing**: Run tests if available (check package.json for test scripts)

## Performance Considerations

1. **Store Optimization**: Ensure proper shallow comparison in Zustand selectors
2. **Memory Management**: Check for memory leaks in subscriptions
3. **Bundle Size**: Monitor app bundle size and optimize imports
4. **React Native Performance**: Profile app performance on devices

## Before Committing

1. **Build Check**: Ensure app builds successfully
2. **Device Testing**: Test on both iOS and Android if possible
3. **Git Status**: Check `git status` for untracked files
4. **Commit Message**: Use descriptive commit messages following conventional commits

## Deployment Preparation

1. **Version Bump**: Update version in package.json and app.json
2. **Changelog**: Update changelog if applicable
3. **Build Testing**: Test production builds
4. **Store Review**: Ensure compliance with app store guidelines
