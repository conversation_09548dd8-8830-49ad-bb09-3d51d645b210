# Code Style and Conventions

## TypeScript Standards

- Strict TypeScript configuration enabled
- All variables and functions must have explicit types
- Interface definitions for complex objects
- Proper error handling with typed errors

## File Organization

- **Slices**: Individual state slices in `lib/store/slices/`
- **Types**: Shared types in `lib/store/types.ts`
- **Hooks**: Custom hooks in `lib/store/hooks/`
- **Components**: React components in `components/`
- **Screens**: App screens in `app/` directory

## Naming Conventions

- **Files**: camelCase for TypeScript files, PascalCase for React components
- **Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Types/Interfaces**: PascalCase
- **Functions**: camelCase with descriptive names

## State Management Patterns

- Slice-based architecture with Zustand
- Immer for immutable updates
- Separate actions and state in each slice
- Proper error handling in async actions
- Loading states for async operations

## React Native Conventions

- Functional components with hooks
- Proper prop typing with interfaces
- Screen-level components in app directory
- Reusable components in components directory
