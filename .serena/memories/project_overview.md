# LiverHealthV2 Project Overview

## Purpose

React Native mobile application for liver health management and tracking. The app helps users monitor their nutrition, medications, health metrics, and provides personalized guidance for liver health improvement.

## Tech Stack

- **Framework**: React Native with Expo
- **State Management**: Zustand with slice-based architecture
- **Backend**: Supabase (authentication, database)
- **Styling**: React Native built-in styling
- **Development**: TypeScript, ESLint, Prettier
- **Build Tool**: Metro bundler
- **Package Manager**: Bun (bun.lock present)

## Key Features

- User authentication and onboarding
- Nutrition tracking and food logging
- Medication management and reminders
- Health metrics monitoring
- Theme management (light/dark/system)
- Settings and notifications

## Architecture

The app uses a slice-based Zustand store architecture with separate slices for:

- Auth (user authentication and profile)
- Nutrition (food tracking, meal planning)
- Medication (medication management and reminders)
- Settings (app preferences and configuration)
- UI (UI state management)
- Theme (theme and appearance management)
