# Suggested Development Commands

## Package Management

- `bun install` - Install dependencies
- `bun add <package>` - Add new dependency
- `bun remove <package>` - Remove dependency

## Development

- `bun start` - Start Expo development server
- `bun run android` - Run on Android device/emulator
- `bun run ios` - Run on iOS device/simulator
- `bun run web` - Run web version

## Code Quality

- `bun run lint` - Run ESLint
- `bun run lint:fix` - Fix ESLint issues automatically
- `bun run format` - Format code with Prettier
- `bun run type-check` - Run TypeScript type checking

## Build & Deploy

- `bun run build` - Build for production
- `expo build:android` - Build Android APK
- `expo build:ios` - Build iOS IPA

## Useful System Commands (macOS)

- `ls -la` - List files with details
- `cd <directory>` - Change directory
- `grep -r "pattern" .` - Search for patterns in files
- `find . -name "*.ts" -o -name "*.tsx"` - Find TypeScript files
- `git status` - Check git status
- `git add .` - Stage all changes
- `git commit -m "message"` - Commit changes
