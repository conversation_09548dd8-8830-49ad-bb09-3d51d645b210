const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Sample data
const organizations = [
  {
    id: '550e8400-e29b-41d4-a716-446655440000',
    name: 'City General Hospital',
    type: 'hospital',
    address: '123 Medical Center Dr, Springfield, IL 62701',
    phone: '(*************',
    website: 'https://citygeneral.health',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    name: 'Liver Specialists Clinic',
    type: 'clinic',
    address: '456 Gastro Ave, Springfield, IL 62702',
    phone: '(*************',
    website: 'https://liverspecialists.com',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    name: 'Springfield Research Institute',
    type: 'research',
    address: '789 Research Blvd, Springfield, IL 62703',
    phone: '(*************',
    website: 'https://springfieldresearch.org',
  },
];

const sampleUsers = [
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    userData: {
      full_name: 'John Smith',
      role: 'patient',
      phone: '(*************',
      date_of_birth: '1975-06-15',
      onboarding_completed: true,
      preferences: { notifications: true, dataSharing: false },
    },
    medicalProfile: {
      liver_condition: 'Hepatitis B',
      diagnosis_date: '2020-03-15',
      current_medications: [
        {
          name: 'Tenofovir',
          dosage: '300',
          unit: 'mg',
          frequency: 'Once daily',
        },
        {
          name: 'Vitamin D',
          dosage: '1000',
          unit: 'IU',
          frequency: 'Once daily',
        },
      ],
      allergies: ['Penicillin', 'Shellfish'],
      severity: 'moderate',
      doctor_name: 'Dr. Sarah Wilson',
      doctor_contact: '<EMAIL>',
      emergency_contact_name: 'Jane Smith',
      emergency_contact_phone: '(*************',
    },
    onboardingData: [
      {
        step: 1,
        completed: true,
        data: {
          fullName: 'John Smith',
          dateOfBirth: '1975-06-15',
          phone: '(*************',
        },
      },
      {
        step: 2,
        completed: true,
        data: {
          liverCondition: 'Hepatitis B',
          diagnosisDate: '2020-03-15',
          severity: 'moderate',
          doctorName: 'Dr. Sarah Wilson',
          doctorContact: '<EMAIL>',
        },
      },
      {
        step: 3,
        completed: true,
        data: {
          emergencyContactName: 'Jane Smith',
          emergencyContactPhone: '(*************',
          emergencyContactRelation: 'Spouse',
        },
      },
      {
        step: 4,
        completed: true,
        data: { notifications: true, dataSharing: false },
      },
    ],
  },
  {
    email: '<EMAIL>',
    password: 'TestPass123!',
    userData: {
      full_name: 'Dr. Sarah Wilson',
      role: 'healthcare_provider',
      phone: '(*************',
      date_of_birth: '1970-11-08',
      onboarding_completed: true,
      preferences: { notifications: true, dataSharing: true },
    },
    onboardingData: [
      {
        step: 1,
        completed: true,
        data: {
          fullName: 'Dr. Sarah Wilson',
          dateOfBirth: '1970-11-08',
          phone: '(*************',
        },
      },
      {
        step: 2,
        completed: true,
        data: { organization: 'City General Hospital', title: 'Hepatologist' },
      },
      {
        step: 3,
        completed: true,
        data: {
          emergencyContactName: 'Dr. James Wilson',
          emergencyContactPhone: '(*************',
          emergencyContactRelation: 'Spouse',
        },
      },
      {
        step: 4,
        completed: true,
        data: { notifications: true, dataSharing: true },
      },
    ],
  },
];

async function seedDatabase() {
  try {
    console.log('Starting database seeding...');

    // Seed organizations
    console.log('Seeding organizations...');
    for (const org of organizations) {
      const { error } = await supabase.from('organizations').upsert(org);

      if (error) {
        console.error('Error seeding organization:', org.name, error);
      } else {
        console.log('✓ Seeded organization:', org.name);
      }
    }

    // Seed users
    console.log('Seeding users...');
    for (const userData of sampleUsers) {
      try {
        // Create auth user
        const { data: authData, error: authError } =
          await supabase.auth.admin.createUser({
            email: userData.email,
            password: userData.password,
            email_confirm: true,
            user_metadata: {
              full_name: userData.userData.full_name,
              role: userData.userData.role,
            },
          });

        if (authError) {
          console.error('Error creating auth user:', userData.email, authError);
          continue;
        }

        console.log('✓ Created auth user:', userData.email);

        // Create user profile
        const { error: profileError } = await supabase.from('users').upsert({
          id: authData.user.id,
          email: userData.email,
          ...userData.userData,
        });

        if (profileError) {
          console.error(
            'Error creating user profile:',
            userData.email,
            profileError,
          );
          continue;
        }

        console.log('✓ Created user profile:', userData.email);

        // Create medical profile if user is a patient
        if (userData.userData.role === 'patient' && userData.medicalProfile) {
          const { error: medicalError } = await supabase
            .from('medical_profiles')
            .upsert({
              user_id: authData.user.id,
              ...userData.medicalProfile,
            });

          if (medicalError) {
            console.error(
              'Error creating medical profile:',
              userData.email,
              medicalError,
            );
          } else {
            console.log('✓ Created medical profile:', userData.email);
          }
        }

        // Create onboarding data
        for (const onboardingStep of userData.onboardingData) {
          const { error: onboardingError } = await supabase
            .from('onboarding_data')
            .upsert({
              user_id: authData.user.id,
              ...onboardingStep,
            });

          if (onboardingError) {
            console.error(
              'Error creating onboarding data:',
              userData.email,
              onboardingStep.step,
              onboardingError,
            );
          }
        }

        console.log('✓ Created onboarding data:', userData.email);
      } catch (error) {
        console.error('Error seeding user:', userData.email, error);
      }
    }

    console.log('Database seeding completed!');
    console.log('\nTest Users Created:');
    console.log('Patient: <EMAIL> (password: TestPass123!)');
    console.log(
      'Healthcare Provider: <EMAIL> (password: TestPass123!)',
    );
  } catch (error) {
    console.error('Fatal error during seeding:', error);
  }
}

// Run the seeding script
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
