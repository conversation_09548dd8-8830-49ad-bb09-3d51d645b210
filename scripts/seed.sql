-- Database seeding script for liver health app
-- This script creates realistic test data for development and testing

-- Insert sample organizations
INSERT INTO organizations (id, name, type, address, phone, website) VALUES
  ('550e8400-e29b-41d4-a716-446655440000', 'City General Hospital', 'hospital', '123 Medical Center Dr, Springfield, IL 62701', '(*************', 'https://citygeneral.health'),
  ('550e8400-e29b-41d4-a716-446655440001', 'Liver Specialists Clinic', 'clinic', '456 Gastro Ave, Springfield, IL 62702', '(*************', 'https://liverspecialists.com'),
  ('550e8400-e29b-41d4-a716-446655440002', 'Springfield Research Institute', 'research', '789 Research Blvd, Springfield, IL 62703', '(*************', 'https://springfieldresearch.org'),
  ('550e8400-e29b-41d4-a716-446655440003', 'Dr. <PERSON> Practice', 'private_practice', '321 Health St, Springfield, IL 62704', '(*************', 'https://drjohnson.med');

-- Insert sample users (these would normally be created through auth.users first)
-- Note: In production, these users would be created through the authentication system
INSERT INTO users (id, email, full_name, role, phone, date_of_birth, onboarding_completed, preferences) VALUES
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '<EMAIL>', 'John Smith', 'patient', '(*************', '1975-06-15', true, '{"notifications": true, "dataSharing": false}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '<EMAIL>', 'Mary Johnson', 'caregiver', '(*************', '1980-03-22', true, '{"notifications": true, "dataSharing": true}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '<EMAIL>', 'Dr. Sarah Wilson', 'healthcare_provider', '(*************', '1970-11-08', true, '{"notifications": true, "dataSharing": true}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', '<EMAIL>', 'Lisa Chen', 'patient', '(*************', '1982-09-12', true, '{"notifications": false, "dataSharing": true}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', '<EMAIL>', 'Bob Rodriguez', 'patient', '(*************', '1965-04-03', false, '{}');

-- Insert user-organization relationships
INSERT INTO user_organizations (user_id, organization_id, role) VALUES
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '550e8400-e29b-41d4-a716-446655440000', 'Hepatologist'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '550e8400-e29b-41d4-a716-446655440001', 'Consulting Physician');

-- Insert medical profiles for patients
INSERT INTO medical_profiles (user_id, liver_condition, diagnosis_date, current_medications, allergies, severity, doctor_name, doctor_contact, emergency_contact_name, emergency_contact_phone) VALUES
  (
    'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
    'Hepatitis B',
    '2020-03-15',
    '[
      {"name": "Tenofovir", "dosage": "300", "unit": "mg", "frequency": "Once daily"},
      {"name": "Vitamin D", "dosage": "1000", "unit": "IU", "frequency": "Once daily"}
    ]',
    ARRAY['Penicillin', 'Shellfish'],
    'moderate',
    'Dr. Sarah Wilson',
    '<EMAIL>',
    'Jane Smith',
    '(*************'
  ),
  (
    'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14',
    'Non-alcoholic fatty liver disease (NAFLD)',
    '2021-08-22',
    '[
      {"name": "Vitamin E", "dosage": "800", "unit": "IU", "frequency": "Once daily"},
      {"name": "Metformin", "dosage": "500", "unit": "mg", "frequency": "Twice daily"}
    ]',
    ARRAY['None known'],
    'mild',
    'Dr. Michael Chen',
    '(*************',
    'David Chen',
    '(*************'
  ),
  (
    'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15',
    'Cirrhosis',
    '2019-12-10',
    '[
      {"name": "Lactulose", "dosage": "30", "unit": "ml", "frequency": "Three times daily"},
      {"name": "Furosemide", "dosage": "40", "unit": "mg", "frequency": "Once daily"},
      {"name": "Spironolactone", "dosage": "100", "unit": "mg", "frequency": "Once daily"}
    ]',
    ARRAY['Sulfa drugs'],
    'severe',
    'Dr. Robert Kumar',
    '(*************',
    'Maria Rodriguez',
    '(*************'
  );

-- Insert onboarding data for completed users
INSERT INTO onboarding_data (user_id, step, completed, data) VALUES
  -- John Smith's onboarding data
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 1, true, '{"fullName": "John Smith", "dateOfBirth": "1975-06-15", "phone": "(*************"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 2, true, '{"liverCondition": "Hepatitis B", "diagnosisDate": "2020-03-15", "severity": "moderate", "doctorName": "Dr. Sarah Wilson", "doctorContact": "<EMAIL>"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 3, true, '{"emergencyContactName": "Jane Smith", "emergencyContactPhone": "(*************", "emergencyContactRelation": "Spouse"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 4, true, '{"notifications": true, "dataSharing": false}'),
  
  -- Mary Johnson's onboarding data
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 1, true, '{"fullName": "Mary Johnson", "dateOfBirth": "1980-03-22", "phone": "(*************"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 2, true, '{"organization": "City General Hospital", "title": "Registered Nurse"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 3, true, '{"emergencyContactName": "Tom Johnson", "emergencyContactPhone": "(*************", "emergencyContactRelation": "Husband"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 4, true, '{"notifications": true, "dataSharing": true}'),
  
  -- Dr. Sarah Wilson's onboarding data
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 1, true, '{"fullName": "Dr. Sarah Wilson", "dateOfBirth": "1970-11-08", "phone": "(*************"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 2, true, '{"organization": "City General Hospital", "title": "Hepatologist"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 3, true, '{"emergencyContactName": "Dr. James Wilson", "emergencyContactPhone": "(*************", "emergencyContactRelation": "Spouse"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 4, true, '{"notifications": true, "dataSharing": true}'),
  
  -- Lisa Chen's onboarding data
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 1, true, '{"fullName": "Lisa Chen", "dateOfBirth": "1982-09-12", "phone": "(*************"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 2, true, '{"liverCondition": "Non-alcoholic fatty liver disease (NAFLD)", "diagnosisDate": "2021-08-22", "severity": "mild", "doctorName": "Dr. Michael Chen", "doctorContact": "(*************"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 3, true, '{"emergencyContactName": "David Chen", "emergencyContactPhone": "(*************", "emergencyContactRelation": "Brother"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 4, true, '{"notifications": false, "dataSharing": true}'),
  
  -- Bob Rodriguez's partial onboarding data (incomplete)
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 1, true, '{"fullName": "Bob Rodriguez", "dateOfBirth": "1965-04-03", "phone": "(*************"}'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 2, false, '{"liverCondition": "Cirrhosis", "diagnosisDate": "2019-12-10", "severity": "severe"}');