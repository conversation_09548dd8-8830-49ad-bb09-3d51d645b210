// Core types for the Zustand store
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  dateOfBirth: Date;
  phone?: string;
  emergencyContact: EmergencyContact;
  profile: UserProfile;
}

export interface EmergencyContact {
  fullName: string;
  relationship: string;
  primaryPhone: string;
  secondaryPhone?: string;
}

export interface UserProfile {
  age: number;
  weight: number;
  height: number;
  liverCondition: string;
  diagnosisDate: Date;
  healthcareProvider: string;
}

export interface FoodItem {
  id: string;
  name: string;
  sodium: number;
  protein: number;
  fat: number;
  calories: number;
  quantity: string;
  isLiverFriendly: boolean;
  warnings: string[];
}

export interface FoodLog {
  id: string;
  foodItem: FoodItem;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  timestamp: Date;
}

export interface Medication {
  id: string;
  name: string;
  dosage: string;
  unit: string;
  frequency: string;
  timingRequirements: string[];
  startDate: Date;
  specialInstructions?: string;
  prescribingDoctor: string;
}

export interface MedicationReminder {
  id: string;
  medicationId: string;
  time: string;
  taken: boolean;
  date: Date;
}

export interface HealthMetric {
  id: string;
  type: 'sodium' | 'protein' | 'fat' | 'calories' | 'water' | 'weight';
  value: number;
  unit: string;
  date: Date;
}

export interface NotificationSettings {
  medicationReminders: boolean;
  mealReminders: boolean;
  waterReminders: boolean;
  weeklyReports: boolean;
  dangerousFood: boolean;
}

export interface AppSettings {
  themePreference: 'light' | 'dark' | 'system'; // Renamed to avoid circular dependency
  notifications: NotificationSettings;
  language: string;
  units: 'metric' | 'imperial';
}

// Loading and error states
export interface AsyncState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
