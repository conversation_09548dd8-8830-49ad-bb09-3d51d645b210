import { StateCreator } from "zustand";
import { AppSettings, NotificationSettings } from "../types";
import AsyncStorage from "@react-native-async-storage/async-storage";

export interface SettingsState {
  settings: AppSettings;
  isLoading: boolean;
  error: string | null;
}

export interface SettingsActions {
  updateThemePreference: (themePreference: "light" | "dark" | "system") => void;
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void;
  updateLanguage: (language: string) => void;
  updateUnits: (units: "metric" | "imperial") => void;
  resetSettings: () => void;
  loadSettings: () => Promise<void>;
  saveSettings: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type SettingsSlice = SettingsState & SettingsActions;

const defaultSettings: AppSettings = {
  themePreference: "system", // Renamed from 'theme' to avoid confusion
  notifications: {
    medicationReminders: true,
    mealReminders: true,
    waterReminders: false,
    weeklyReports: true,
    dangerousFood: true,
  },
  language: "en",
  units: "metric",
};

export const createSettingsSlice: StateCreator<
  SettingsSlice,
  [],
  [],
  SettingsSlice
> = (set, get) => ({
  // Initial state
  settings: defaultSettings,
  isLoading: false,
  error: null,

  // Actions
  updateThemePreference: (themePreference: "light" | "dark" | "system") => {
    set((state) => ({
      settings: {
        ...state.settings,
        themePreference,
      },
    }));
  },

  updateNotificationSettings: (
    notificationUpdates: Partial<NotificationSettings>,
  ) => {
    set((state) => ({
      settings: {
        ...state.settings,
        notifications: {
          ...state.settings.notifications,
          ...notificationUpdates,
        },
      },
    }));
  },

  updateLanguage: (language: string) => {
    set((state) => ({
      settings: {
        ...state.settings,
        language,
      },
    }));
  },

  updateUnits: (units: "metric" | "imperial") => {
    set((state) => ({
      settings: {
        ...state.settings,
        units,
      },
    }));
  },

  resetSettings: () => {
    set({ settings: defaultSettings });
  },

  loadSettings: async () => {
    set({ isLoading: true, error: null });

    try {
      const savedSettings = await AsyncStorage.getItem("@app_settings");
      if (savedSettings) {
        set({ settings: JSON.parse(savedSettings), isLoading: false });
      } else {
        set({ isLoading: false });
      }

      set({ isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error
          ? error.message
          : "Failed to load settings",
        isLoading: false,
      });
    }
  },

  saveSettings: async () => {
    const { settings } = get();

    try {
      await AsyncStorage.setItem("@app_settings", JSON.stringify(settings));
    } catch (error) {
      set({
        error: error instanceof Error
          ? error.message
          : "Failed to save settings",
      });
    }
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },
});
