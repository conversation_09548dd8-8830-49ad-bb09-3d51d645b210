import { StateCreator } from "zustand";

export interface UIState {
  isPageVisible: boolean;
  isLoading: boolean;
  progress: number;
  activeTab: string;
  modals: {
    addFood: boolean;
    camera: boolean;
    settings: boolean;
  };
  notifications: Array<{
    id: string;
    type: "success" | "error" | "warning" | "info";
    message: string;
    timestamp: Date;
    timeoutId?: ReturnType<typeof setTimeout>;
  }>;
}

export interface UIActions {
  setPageVisible: (visible: boolean) => void;
  setLoading: (loading: boolean) => void;
  setProgress: (progress: number) => void;
  setActiveTab: (tab: string) => void;
  openModal: (modal: keyof UIState["modals"]) => void;
  closeModal: (modal: keyof UIState["modals"]) => void;
  closeAllModals: () => void;
  addNotification: (
    notification: Omit<
      UIState["notifications"][0],
      "id" | "timestamp" | "timeoutId"
    >,
  ) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

export type UISlice = UIState & UIActions;

export const createUISlice: StateCreator<UISlice, [], [], UISlice> = (
  set,
  get,
) => ({
  // Initial state
  isPageVisible: true,
  isLoading: false,
  progress: 0,
  activeTab: "index",
  modals: {
    addFood: false,
    camera: false,
    settings: false,
  },
  notifications: [],

  // Actions
  setPageVisible: (visible: boolean) => {
    set({ isPageVisible: visible });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setProgress: (progress: number) => {
    set({ progress });
  },

  setActiveTab: (tab: string) => {
    set({ activeTab: tab });
  },

  openModal: (modal: keyof UIState["modals"]) => {
    set((state) => ({
      modals: {
        ...state.modals,
        [modal]: true,
      },
    }));
  },

  closeModal: (modal: keyof UIState["modals"]) => {
    set((state) => ({
      modals: {
        ...state.modals,
        [modal]: false,
      },
    }));
  },

  closeAllModals: () => {
    set({
      modals: {
        addFood: false,
        camera: false,
        settings: false,
      },
    });
  },

  addNotification: (
    notification: Omit<
      UIState["notifications"][0],
      "id" | "timestamp" | "timeoutId"
    >,
  ) => {
    const id = Date.now().toString();

    // Auto-remove notification after 5 seconds with proper cleanup
    const timeoutId = setTimeout(() => {
      get().removeNotification(id);
    }, 5000);

    const newNotification = {
      ...notification,
      id,
      timestamp: new Date(),
      timeoutId,
    };

    set((state) => ({
      notifications: [...state.notifications, newNotification],
    }));
  },

  removeNotification: (id: string) => {
    set((state) => {
      // Clear timeout for the notification being removed
      const notificationToRemove = state.notifications.find((n) => n.id === id);
      if (notificationToRemove?.timeoutId) {
        clearTimeout(notificationToRemove.timeoutId);
      }

      return {
        notifications: state.notifications.filter(
          (notification) => notification.id !== id,
        ),
      };
    });
  },

  clearNotifications: () => {
    set((state) => {
      // Clear all timeouts before clearing notifications
      state.notifications.forEach((notification) => {
        if (notification.timeoutId) {
          clearTimeout(notification.timeoutId);
        }
      });

      return { notifications: [] };
    });
  },
});
