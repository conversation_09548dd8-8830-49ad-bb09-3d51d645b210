import { useCallback, useState } from "react";
import { Alert } from "react-native";
import { router } from "expo-router";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { type SignUpFormData, signUpSchema } from "@/types/auth";
import { useAuth } from "@/lib/hooks/useAuth";

interface UseSignUpFormReturn
  extends ReturnType<typeof useForm<SignUpFormData>> {
  // Custom methods for compatibility
  loading: boolean;
  onSubmit: (data: SignUpFormData) => Promise<void>;
}

export function useSignUpForm(): UseSignUpFormReturn {
  const { signUp } = useAuth();
  const [loading, setLoading] = useState(false);

  // Initialize react-hook-form with Zod validation
  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema as any), // Type assertion for Zod v4 compatibility
    defaultValues: {
      emailAddress: "",
      password: "",
      confirmPassword: "",
      fullName: "",
      role: "patient",
    },
    mode: "onBlur", // Validate on blur for real-time feedback
  });

  // Handle form submission with react-hook-form
  const onSubmit = useCallback(
    async (data: SignUpFormData) => {
      setLoading(true);
      try {
        const { error } = await signUp(data.emailAddress, data.password, {
          full_name: data.fullName,
          role: data.role,
        });

        if (error) {
          Alert.alert("Sign Up Failed", error.message);
        } else {
          Alert.alert(
            "Success",
            "Account created! Please check your email for verification.",
            [{ text: "OK", onPress: () => router.replace("/(auth)/sign-in") }],
          );
        }
      } catch (error) {
        console.error("Sign up error:", error);
        Alert.alert("Error", "An unexpected error occurred");
      } finally {
        setLoading(false);
      }
    },
    [signUp],
  );

  return { ...form, loading, onSubmit };
}
