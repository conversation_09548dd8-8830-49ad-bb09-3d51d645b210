import { useEffect, useState } from 'react';
import { Href, useRouter, useSegments } from 'expo-router';

interface UsePageTransitionOptions {
  enableProgressBar?: boolean;
  progressDuration?: number;
  transitionDelay?: number;
}

export function usePageTransition(options: UsePageTransitionOptions = {}) {
  const {
    enableProgressBar = true,
    progressDuration = 800,
    transitionDelay = 100,
  } = options;

  const router = useRouter();
  const segments = useSegments();

  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isPageVisible, setIsPageVisible] = useState(true);

  // Simulate loading progress
  const simulateProgress = () => {
    setProgress(0);
    setIsLoading(true);

    const steps = [
      { progress: 25, delay: progressDuration * 0.2 },
      { progress: 50, delay: progressDuration * 0.4 },
      { progress: 75, delay: progressDuration * 0.7 },
      { progress: 100, delay: progressDuration },
    ];

    steps.forEach(({ progress: targetProgress, delay }) => {
      setTimeout(() => {
        setProgress(targetProgress);
        if (targetProgress === 100) {
          setTimeout(() => {
            setIsLoading(false);
            setIsPageVisible(true);
          }, 300);
        }
      }, delay);
    });
  };

  // Initialize page visibility on mount
  useEffect(() => {
    setIsPageVisible(true);
  }, []);

  // Navigate with transition
  const navigateWithTransition = (href: Href) => {
    setIsPageVisible(false);

    setTimeout(() => {
      if (enableProgressBar) {
        simulateProgress();
      }
      router.push(href);
    }, transitionDelay);
  };

  // Replace with transition
  const replaceWithTransition = (href: Href) => {
    setIsPageVisible(false);

    setTimeout(() => {
      if (enableProgressBar) {
        simulateProgress();
      }
      router.replace(href);
    }, transitionDelay);
  };

  // Back with transition
  const backWithTransition = () => {
    setIsPageVisible(false);

    setTimeout(() => {
      router.back();
      setIsPageVisible(true);
    }, transitionDelay);
  };

  // Reset page visibility on route change
  useEffect(() => {
    if (!enableProgressBar) {
      setIsPageVisible(true);
    }
  }, [segments, enableProgressBar]);

  return {
    isLoading,
    progress,
    isPageVisible,
    navigateWithTransition,
    replaceWithTransition,
    backWithTransition,
    setIsPageVisible,
  };
}
