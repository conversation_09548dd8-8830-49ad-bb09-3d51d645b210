import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Heart,
  Droplets,
  Scale,
  Share,
  FileText,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/lib/store/selectors';

const screenWidth = Dimensions.get('window').width;

interface HealthMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  status: 'good' | 'warning' | 'danger';
  icon: React.ReactNode;
  color: string;
}

interface ProgressData {
  date: string;
  sodium: number;
  protein: number;
  water: number;
  weight: number;
}

export default function HealthMetricsScreen() {
  const colors = useThemeColors();

  const [selectedPeriod, setSelectedPeriod] = useState<
    'week' | 'month' | '3months'
  >('week');

  // Sample progress data for the last 7 days
  const progressData: ProgressData[] = [
    { date: '2025-01-13', sodium: 1800, protein: 45, water: 8, weight: 75.2 },
    { date: '2025-01-14', sodium: 2100, protein: 42, water: 7, weight: 75.1 },
    { date: '2025-01-15', sodium: 1650, protein: 48, water: 9, weight: 75.0 },
    { date: '2025-01-16', sodium: 1900, protein: 44, water: 8, weight: 74.9 },
    { date: '2025-01-17', sodium: 1750, protein: 46, water: 8, weight: 74.8 },
    { date: '2025-01-18', sodium: 2050, protein: 43, water: 7, weight: 74.9 },
    { date: '2025-01-19', sodium: 1850, protein: 47, water: 9, weight: 74.7 },
  ];

  const currentMetrics: HealthMetric[] = [
    {
      id: '1',
      name: 'Avg Daily Sodium',
      value: 1850,
      unit: 'mg',
      trend: 'down',
      trendPercentage: 8.2,
      status: 'good',
      icon: <Activity size={24} color={colors.success} />,
      color: colors.success,
    },
    {
      id: '2',
      name: 'Protein Intake',
      value: 45,
      unit: 'g/day',
      trend: 'up',
      trendPercentage: 5.1,
      status: 'good',
      icon: <Heart size={24} color={colors.primary} />,
      color: colors.primary,
    },
    {
      id: '3',
      name: 'Water Intake',
      value: 8,
      unit: 'glasses/day',
      trend: 'stable',
      trendPercentage: 0,
      status: 'good',
      icon: <Droplets size={24} color={colors.info} />,
      color: colors.info,
    },
    {
      id: '4',
      name: 'Body Weight',
      value: 74.7,
      unit: 'kg',
      trend: 'down',
      trendPercentage: 2.1,
      status: 'good',
      icon: <Scale size={24} color={colors.warning} />,
      color: colors.warning,
    },
  ];

  const generateReport = () => {
    // This would generate a comprehensive health report
    console.log('Generating health report...');
  };

  const shareReport = () => {
    // This would share the report with healthcare providers
    console.log('Sharing report...');
  };

  const renderTrendIcon = (trend: string, trendPercentage: number) => {
    if (trend === 'up') {
      return <TrendingUp size={16} color="#EF4444" />;
    } else if (trend === 'down') {
      return <TrendingDown size={16} color="#10B981" />;
    }
    return null;
  };

  const renderSimpleChart = (data: number[], color: string) => {
    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const range = maxValue - minValue || 1;

    return (
      <View style={styles.chartContainer}>
        <View style={styles.chartBars}>
          {data.map((value, index) => {
            const heightPercentage = ((value - minValue) / range) * 100;
            return (
              <View
                key={value}
                style={[
                  styles.chartBar,
                  {
                    height: `${Math.max(heightPercentage, 10)}%`,
                    backgroundColor: color,
                  },
                ]}
              />
            );
          })}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Health Metrics
          </Text>
          <Text
            style={[styles.headerSubtitle, { color: colors.textSecondary }]}
          >
            Track your liver health progress
          </Text>
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          {[
            { key: 'week', label: '7 Days' },
            { key: 'month', label: '30 Days' },
            { key: '3months', label: '3 Months' },
          ].map(period => (
            <TouchableOpacity
              key={period.key}
              style={[
                styles.periodButton,
                {
                  backgroundColor: colors.surface,
                  borderColor: colors.border,
                },
                selectedPeriod === period.key && {
                  backgroundColor: colors.primary,
                  borderColor: colors.primary,
                },
              ]}
              onPress={() => setSelectedPeriod(period.key as any)}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  { color: colors.textSecondary },
                  selectedPeriod === period.key && {
                    color: colors.textInverse,
                  },
                ]}
              >
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Current Metrics Cards */}
        <View style={styles.metricsGrid}>
          {currentMetrics.map(metric => (
            <View
              key={metric.id}
              style={[styles.metricCard, { backgroundColor: colors.surface }]}
            >
              <View style={styles.metricHeader}>
                {metric.icon}
                <View style={styles.metricTrend}>
                  {renderTrendIcon(metric.trend, metric.trendPercentage)}
                  {metric.trendPercentage > 0 && (
                    <Text
                      style={[
                        styles.trendPercentage,
                        {
                          color: metric.trend === 'up' ? '#EF4444' : '#10B981',
                        },
                      ]}
                    >
                      {metric.trendPercentage}%
                    </Text>
                  )}
                </View>
              </View>
              <Text
                style={[styles.metricName, { color: colors.textSecondary }]}
              >
                {metric.name}
              </Text>
              <View style={styles.metricValueContainer}>
                <Text style={[styles.metricValue, { color: metric.color }]}>
                  {metric.value}
                </Text>
                <Text
                  style={[styles.metricUnit, { color: colors.textSecondary }]}
                >
                  {metric.unit}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Progress Charts */}
        <View style={styles.chartsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Progress Overview
          </Text>

          <View style={[styles.chartCard, { backgroundColor: colors.surface }]}>
            <View style={styles.chartHeader}>
              <Text style={[styles.chartTitle, { color: colors.text }]}>
                Daily Sodium Intake
              </Text>
              <Text
                style={[styles.chartTarget, { color: colors.textSecondary }]}
              >
                Target: &lt; 2000mg
              </Text>
            </View>
            {renderSimpleChart(
              progressData.map(d => d.sodium),
              '#EF4444'
            )}
            <View style={styles.chartLabels}>
              {progressData.slice(-7).map(data => (
                <Text
                  key={data.date}
                  style={[styles.chartLabel, { color: colors.textTertiary }]}
                >
                  {new Date(data.date).getDate()}
                </Text>
              ))}
            </View>
          </View>

          <View style={[styles.chartCard, { backgroundColor: colors.surface }]}>
            <View style={styles.chartHeader}>
              <Text style={[styles.chartTitle, { color: colors.text }]}>
                Protein Intake
              </Text>
              <Text
                style={[styles.chartTarget, { color: colors.textSecondary }]}
              >
                Target: 40-50g
              </Text>
            </View>
            {renderSimpleChart(
              progressData.map(d => d.protein),
              '#14B8A6'
            )}
            <View style={styles.chartLabels}>
              {progressData.slice(-7).map(data => (
                <Text
                  key={data.date}
                  style={[styles.chartLabel, { color: colors.textTertiary }]}
                >
                  {new Date(data.date).getDate()}
                </Text>
              ))}
            </View>
          </View>

          <View style={[styles.chartCard, { backgroundColor: colors.surface }]}>
            <View style={styles.chartHeader}>
              <Text style={[styles.chartTitle, { color: colors.text }]}>
                Water Intake
              </Text>
              <Text
                style={[styles.chartTarget, { color: colors.textSecondary }]}
              >
                Target: 8 glasses
              </Text>
            </View>
            {renderSimpleChart(
              progressData.map(d => d.water),
              '#3B82F6'
            )}
            <View style={styles.chartLabels}>
              {progressData.slice(-7).map(data => (
                <Text
                  key={data.date}
                  style={[styles.chartLabel, { color: colors.textTertiary }]}
                >
                  {new Date(data.date).getDate()}
                </Text>
              ))}
            </View>
          </View>
        </View>

        {/* Health Insights */}
        <View style={styles.insightsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Health Insights
          </Text>
          <View
            style={[styles.insightCard, { backgroundColor: colors.surface }]}
          >
            <View style={styles.insightHeader}>
              <TrendingDown size={20} color={colors.success} />
              <Text style={[styles.insightTitle, { color: colors.text }]}>
                Sodium Reduction
              </Text>
            </View>
            <Text style={[styles.insightText, { color: colors.textSecondary }]}>
              Great progress! Your sodium intake has decreased by 8.2% this
              week. This helps reduce fluid retention and supports liver
              function.
            </Text>
          </View>

          <View
            style={[styles.insightCard, { backgroundColor: colors.surface }]}
          >
            <View style={styles.insightHeader}>
              <Heart size={20} color={colors.primary} />
              <Text style={[styles.insightTitle, { color: colors.text }]}>
                Protein Balance
              </Text>
            </View>
            <Text style={[styles.insightText, { color: colors.textSecondary }]}>
              Your protein intake is well within the recommended range.
              Maintaining adequate protein supports liver regeneration.
            </Text>
          </View>
        </View>

        {/* Export Actions */}
        <View style={styles.exportSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Share with Healthcare Team
          </Text>
          <View style={styles.exportButtons}>
            <TouchableOpacity
              style={[
                styles.exportButton,
                {
                  backgroundColor: colors.surface,
                  borderColor: colors.primary,
                },
              ]}
              onPress={generateReport}
            >
              <FileText size={20} color={colors.primary} />
              <Text
                style={[styles.exportButtonText, { color: colors.primary }]}
              >
                Generate Report
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.exportButton,
                {
                  backgroundColor: colors.surface,
                  borderColor: colors.primary,
                },
              ]}
              onPress={shareReport}
            >
              <Share size={20} color={colors.primary} />
              <Text
                style={[styles.exportButtonText, { color: colors.primary }]}
              >
                Share Data
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 4,
  },
  periodButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    textAlign: 'center',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  metricCard: {
    width: '48%',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    marginHorizontal: '1%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTrend: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendPercentage: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
    marginLeft: 4,
  },
  metricName: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginBottom: 8,
  },
  metricValueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  metricValue: {
    fontFamily: 'Inter-Bold',
    fontSize: 20,
  },
  metricUnit: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginLeft: 4,
  },
  chartsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 20,
    marginBottom: 16,
  },
  chartCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  chartTarget: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  chartContainer: {
    height: 80,
    marginBottom: 8,
  },
  chartBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: '100%',
    paddingHorizontal: 8,
  },
  chartBar: {
    width: 20,
    borderRadius: 2,
    marginHorizontal: 2,
  },
  chartLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  chartLabel: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    width: 24,
    textAlign: 'center',
  },
  insightsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  insightCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
  insightText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    lineHeight: 20,
  },
  exportSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  exportButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  exportButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  exportButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
    marginLeft: 8,
  },
});
