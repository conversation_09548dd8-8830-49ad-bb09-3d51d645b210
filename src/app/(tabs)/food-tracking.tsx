import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import {
  Camera,
  Search,
  Plus,
  TriangleAlert as <PERSON><PERSON><PERSON><PERSON>gle,
  CircleCheck as CheckCircle,
  Circle as XCircle,
  Utensils,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CameraView, useCameraPermissions } from 'expo-camera';
import {
  useNutritionActions,
  useMealLogs,
  useUIActions,
  useModals,
} from '@/lib/store';
import { FOOD_DATABASE } from '@/lib/data/foodDatabase';
import {
  useValidation,
  useThemeColors,
  useDailyNutritionMemo,
  useIsDarkTheme,
} from '@/lib/store/selectors';
import { FoodItem } from '@/lib/store/types';

export default function FoodTrackingScreen() {
  const isDark = useIsDarkTheme();
  const colors = useThemeColors();
  const { openModal, closeModal } = useUIActions();
  const modals = useModals();

  const nutrients = useDailyNutritionMemo();
  const { addFoodWithValidation } = useValidation();
  const { searchFoodDatabase } = useNutritionActions();
  const getMealLogs = useMealLogs;

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMealType, setSelectedMealType] = useState<
    'breakfast' | 'lunch' | 'dinner' | 'snack'
  >('breakfast');
  const [permission, requestPermission] = useCameraPermissions();

  const filteredFoods = searchFoodDatabase(searchQuery);

  const addFoodToLog = (foodItem: FoodItem) => {
    addFoodWithValidation(foodItem, selectedMealType);
    closeModal('addFood');
  };

  const openCamera = async () => {
    if (!permission) {
      const { granted } = await requestPermission();
      if (!granted) {
        Alert.alert(
          'Permission needed',
          'Camera permission is required to scan barcodes'
        );
        return;
      }
    }
    openModal('camera');
  };

  const handleBarcodeScan = (data: any) => {
    closeModal('camera');
    // Simulate barcode lookup
    Alert.alert(
      'Barcode Scanned',
      'Food item found: Canned Tomato Soup\nHigh sodium content detected!',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Add to Log', onPress: () => addFoodToLog(FOOD_DATABASE[1]) },
      ]
    );
  };

  // nutrients now comes from the hook above

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Food Tracking
          </Text>
          <Text
            style={[styles.headerSubtitle, { color: colors.textSecondary }]}
          >
            Monitor your liver-friendly nutrition
          </Text>
        </View>

        {/* Daily Summary */}
        <View style={[styles.summaryCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            Today&apos;s Intake
          </Text>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryItem}>
              <Text
                style={[
                  styles.summaryValue,
                  { color: nutrients.sodium > 2000 ? '#EF4444' : '#10B981' },
                ]}
              >
                {nutrients.sodium}mg
              </Text>
              <Text
                style={[styles.summaryLabel, { color: colors.textSecondary }]}
              >
                Sodium
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#14B8A6' }]}>
                {nutrients.protein}g
              </Text>
              <Text
                style={[styles.summaryLabel, { color: colors.textSecondary }]}
              >
                Protein
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#F59E0B' }]}>
                {nutrients.fat}g
              </Text>
              <Text
                style={[styles.summaryLabel, { color: colors.textSecondary }]}
              >
                Fat
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryValue, { color: '#6366F1' }]}>
                {nutrients.calories}
              </Text>
              <Text
                style={[styles.summaryLabel, { color: colors.textSecondary }]}
              >
                Calories
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary }]}
            onPress={openCamera}
          >
            <Camera size={20} color={colors.textInverse} />
            <Text
              style={[styles.primaryButtonText, { color: colors.textInverse }]}
            >
              Scan Barcode
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.secondaryButton,
              {
                backgroundColor: colors.surface,
                borderColor: colors.primary,
              },
            ]}
            onPress={() => openModal('addFood')}
          >
            <Plus size={20} color={colors.primary} />
            <Text
              style={[styles.secondaryButtonText, { color: colors.primary }]}
            >
              Add Food
            </Text>
          </TouchableOpacity>
        </View>

        {/* Meal Sections */}
        {['breakfast', 'lunch', 'dinner', 'snack'].map(mealType => (
          <View key={mealType} style={styles.mealSection}>
            <View style={styles.mealHeader}>
              <Utensils size={20} color={colors.primary} />
              <Text style={[styles.mealTitle, { color: colors.text }]}>
                {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
              </Text>
            </View>
            <View
              style={[styles.mealContent, { backgroundColor: colors.surface }]}
            >
              {getMealLogs(mealType).length === 0 ? (
                <Text
                  style={[styles.emptyMealText, { color: colors.textTertiary }]}
                >
                  No food logged yet
                </Text>
              ) : (
                getMealLogs(mealType).map(log => (
                  <View key={log.id} style={styles.foodLogItem}>
                    <View style={styles.foodLogHeader}>
                      <Text
                        style={[styles.foodLogName, { color: colors.text }]}
                      >
                        {log.foodItem.name}
                      </Text>
                      {log.foodItem.isLiverFriendly ? (
                        <CheckCircle size={16} color="#10B981" />
                      ) : (
                        <XCircle size={16} color="#EF4444" />
                      )}
                    </View>
                    <Text
                      style={[
                        styles.foodLogQuantity,
                        { color: colors.textSecondary },
                      ]}
                    >
                      {log.foodItem.quantity}
                    </Text>
                    <View style={styles.foodLogNutrients}>
                      <Text
                        style={[
                          styles.nutrientText,
                          { color: colors.textSecondary },
                        ]}
                      >
                        {log.foodItem.sodium}mg sodium • {log.foodItem.protein}g
                        protein • {log.foodItem.fat}g fat
                      </Text>
                    </View>
                    {log.foodItem.warnings.length > 0 && (
                      <View
                        style={[
                          styles.warningContainer,
                          {
                            backgroundColor: isDark
                              ? 'rgba(239, 68, 68, 0.1)'
                              : '#FEF2F2',
                          },
                        ]}
                      >
                        <AlertTriangle size={14} color="#EF4444" />
                        <Text
                          style={[styles.warningText, { color: colors.error }]}
                        >
                          {log.foodItem.warnings[0]}
                        </Text>
                      </View>
                    )}
                  </View>
                ))
              )}
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Camera Modal */}
      <Modal visible={modals.camera} animationType="slide">
        <View style={styles.cameraContainer}>
          {permission?.granted ? (
            <CameraView
              style={styles.camera}
              onBarcodeScanned={handleBarcodeScan}
            >
              <View style={styles.cameraOverlay}>
                <Text style={styles.cameraInstructions}>
                  Point camera at barcode to scan
                </Text>
                <TouchableOpacity
                  style={styles.cameraCloseButton}
                  onPress={() => closeModal('camera')}
                >
                  <Text style={styles.cameraCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
            </CameraView>
          ) : (
            <View style={styles.permissionContainer}>
              <Text style={styles.permissionText}>
                Camera permission is required to scan barcodes
              </Text>
              <TouchableOpacity
                style={styles.permissionButton}
                onPress={requestPermission}
              >
                <Text style={styles.permissionButtonText}>
                  Grant Permission
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Modal>

      {/* Add Food Modal */}
      <Modal visible={modals.addFood} animationType="slide">
        <SafeAreaView
          style={[
            styles.modalContainer,
            { backgroundColor: colors.background },
          ]}
        >
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Add Food
            </Text>
            <TouchableOpacity onPress={() => closeModal('addFood')}>
              <Text style={[styles.modalClose, { color: colors.primary }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>

          {/* Meal Type Selector */}
          <View style={styles.mealSelector}>
            {['breakfast', 'lunch', 'dinner', 'snack'].map(meal => (
              <TouchableOpacity
                key={meal}
                style={[
                  styles.mealOption,
                  {
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                  },
                  selectedMealType === meal && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() => setSelectedMealType(meal as any)}
              >
                <Text
                  style={[
                    styles.mealOptionText,
                    { color: colors.textSecondary },
                    selectedMealType === meal && {
                      color: colors.textInverse,
                    },
                  ]}
                >
                  {meal.charAt(0).toUpperCase() + meal.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Search */}
          <View
            style={[
              styles.searchContainer,
              { backgroundColor: colors.surface },
            ]}
          >
            <Search size={20} color={colors.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder="Search for food..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={colors.textTertiary}
            />
          </View>

          {/* Food List */}
          <ScrollView style={styles.foodList}>
            {filteredFoods.map(food => (
              <TouchableOpacity
                key={food.id}
                style={[styles.foodItem, { backgroundColor: colors.surface }]}
                onPress={() => addFoodToLog(food)}
              >
                <View style={styles.foodItemHeader}>
                  <Text style={[styles.foodItemName, { color: colors.text }]}>
                    {food.name}
                  </Text>
                  {food.isLiverFriendly ? (
                    <CheckCircle size={20} color="#10B981" />
                  ) : (
                    <XCircle size={20} color="#EF4444" />
                  )}
                </View>
                <Text
                  style={[
                    styles.foodItemQuantity,
                    { color: colors.textSecondary },
                  ]}
                >
                  {food.quantity}
                </Text>
                <View style={styles.foodItemNutrients}>
                  <Text
                    style={[
                      styles.nutrientText,
                      { color: colors.textSecondary },
                    ]}
                  >
                    {food.sodium}mg sodium • {food.protein}g protein •{' '}
                    {food.fat}g fat
                  </Text>
                </View>
                {food.warnings.length > 0 && (
                  <View
                    style={[
                      styles.warningContainer,
                      {
                        backgroundColor: isDark
                          ? 'rgba(239, 68, 68, 0.1)'
                          : '#FEF2F2',
                      },
                    ]}
                  >
                    <AlertTriangle size={14} color="#EF4444" />
                    <Text style={[styles.warningText, { color: colors.error }]}>
                      {food.warnings[0]}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  summaryCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontFamily: 'Inter-Bold',
    fontSize: 20,
    marginBottom: 4,
  },
  summaryLabel: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  primaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginRight: 8,
  },
  primaryButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginLeft: 8,
    borderWidth: 1,
  },
  secondaryButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
  mealSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  mealHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  mealTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginLeft: 8,
  },
  mealContent: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  emptyMealText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    textAlign: 'center',
    padding: 20,
  },
  foodLogItem: {
    padding: 16,
  },
  foodLogHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  foodLogName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  foodLogQuantity: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginBottom: 8,
  },
  foodLogNutrients: {
    marginBottom: 8,
  },
  nutrientText: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
  },
  warningText: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 60,
  },
  cameraInstructions: {
    fontFamily: 'Inter-Medium',
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 16,
    borderRadius: 8,
  },
  cameraCloseButton: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  cameraCloseText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#FFFFFF',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionText: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 24,
  },
  permissionButton: {
    backgroundColor: '#14B8A6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#FFFFFF',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 20,
  },
  modalClose: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  mealSelector: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  mealOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
  },
  mealOptionText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    marginLeft: 12,
  },
  foodList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  foodItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  foodItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  foodItemName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  foodItemQuantity: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginBottom: 8,
  },
  foodItemNutrients: {
    marginBottom: 8,
  },
});
