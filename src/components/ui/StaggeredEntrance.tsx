import React, { useEffect } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withDelay,
  withTiming,
  withSequence,
  Easing,
} from 'react-native-reanimated';

interface StaggeredEntranceProps {
  readonly children: React.ReactNode;
  readonly delay?: number;
  readonly isVisible: boolean;
  readonly animationType?:
    | 'fadeUp'
    | 'fadeDown'
    | 'fadeLeft'
    | 'fadeRight'
    | 'scale';
  readonly duration?: number;
}

export default function StaggeredEntrance({
  children,
  delay = 0,
  isVisible,
  animationType = 'fadeUp',
  duration = 400,
}: StaggeredEntranceProps) {
  const opacity = useSharedValue(0);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);

  // Set initial values based on animation type
  useEffect(() => {
    switch (animationType) {
      case 'fadeUp':
        translateY.value = 30;
        break;
      case 'fadeDown':
        translateY.value = -30;
        break;
      case 'fadeLeft':
        translateX.value = 30;
        break;
      case 'fadeRight':
        translateX.value = -30;
        break;
      case 'scale':
        scale.value = 0.8;
        break;
    }
  }, [animationType, scale, translateX, translateY]);

  useEffect(() => {
    if (isVisible) {
      // Entrance animation with delay
      opacity.value = withDelay(
        delay,
        withTiming(1, {
          duration,
          easing: Easing.out(Easing.cubic),
        })
      );

      translateX.value = withDelay(
        delay,
        withTiming(0, {
          duration,
          easing: Easing.out(Easing.cubic),
        })
      );

      translateY.value = withDelay(
        delay,
        withTiming(0, {
          duration,
          easing: Easing.out(Easing.cubic),
        })
      );

      if (animationType === 'scale') {
        scale.value = withDelay(
          delay,
          withSequence(
            withTiming(1.05, {
              duration: duration * 0.6,
              easing: Easing.out(Easing.back(1.2)),
            }),
            withTiming(1, {
              duration: duration * 0.4,
              easing: Easing.out(Easing.quad),
            })
          )
        );
      }
    } else {
      // Exit animation
      opacity.value = withTiming(0, {
        duration: 200,
        easing: Easing.in(Easing.cubic),
      });

      // Reset to initial positions
      switch (animationType) {
        case 'fadeUp':
          translateY.value = withTiming(15, { duration: 200 });
          break;
        case 'fadeDown':
          translateY.value = withTiming(-15, { duration: 200 });
          break;
        case 'fadeLeft':
          translateX.value = withTiming(15, { duration: 200 });
          break;
        case 'fadeRight':
          translateX.value = withTiming(-15, { duration: 200 });
          break;
        case 'scale':
          scale.value = withTiming(0.9, { duration: 200 });
          break;
      }
    }
  }, [
    isVisible,
    delay,
    animationType,
    duration,
    opacity,
    translateX,
    translateY,
    scale,
  ]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  return <Animated.View style={animatedStyle}>{children}</Animated.View>;
}
