import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import StaggeredEntrance from './StaggeredEntrance';
import { useThemeColors } from '@/lib/store/selectors';

const STAGGER_DELAY = 100;

interface AnimatedHeaderProps {
  readonly title: string;
  readonly subtitle?: string;
  readonly isVisible: boolean;
  readonly delay?: number;
}

export default function AnimatedHeader({
  title,
  subtitle,
  isVisible,
  delay = 0,
}: Readonly<AnimatedHeaderProps>) {
  const colors = useThemeColors();

  return (
    <View style={styles.container}>
      <StaggeredEntrance
        isVisible={isVisible}
        delay={delay}
        animationType="fadeDown"
        duration={500}
      >
        <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
      </StaggeredEntrance>

      {subtitle && (
        <StaggeredEntrance
          isVisible={isVisible}
          delay={delay + STAGGER_DELAY}
          animationType="fadeDown"
          duration={500}
        >
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {subtitle}
          </Text>
        </StaggeredEntrance>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
  },
  title: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    marginBottom: 4,
  },
  subtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
});
