import { useThemeColors } from '@/lib/store/selectors';
import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  interpolate,
  runOnJS,
  Easing,
} from 'react-native-reanimated';

interface LoadingProgressBarProps {
  readonly progress: number; // 0-100
  readonly isVisible: boolean;
  readonly onComplete?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export default function LoadingProgressBar({
  progress,
  isVisible,
  onComplete,
}: LoadingProgressBarProps) {
  const colors = useThemeColors();
  const progressValue = useSharedValue(0);
  const pulseValue = useSharedValue(1);
  const opacityValue = useSharedValue(0);

  useEffect(() => {
    if (isVisible) {
      opacityValue.value = withTiming(1, { duration: 200 });
    } else {
      opacityValue.value = withTiming(0, { duration: 300 });
    }
  }, [isVisible, opacityValue]);

  useEffect(() => {
    progressValue.value = withTiming(progress, {
      duration: 800,
      easing: Easing.out(Easing.cubic),
    });

    // Pulse effect at key milestones
    if (progress === 25 || progress === 50 || progress === 75) {
      pulseValue.value = withSequence(
        withTiming(1.1, { duration: 150 }),
        withTiming(1, { duration: 150 })
      );
    }

    // Complete animation
    if (progress >= 100) {
      setTimeout(() => {
        opacityValue.value = withTiming(
          0,
          {
            duration: 400,
            easing: Easing.out(Easing.quad),
          },
          () => {
            if (onComplete) {
              runOnJS(onComplete)();
            }
          }
        );
      }, 500);
    }
  }, [onComplete, opacityValue, progress, progressValue, pulseValue]);

  const progressBarStyle = useAnimatedStyle(() => {
    const width = interpolate(progressValue.value, [0, 100], [0, screenWidth]);

    return {
      width,
      transform: [{ scaleY: pulseValue.value }],
    };
  });

  const containerStyle = useAnimatedStyle(() => ({
    opacity: opacityValue.value,
  }));

  const glowStyle = useAnimatedStyle(() => {
    const glowOpacity = interpolate(progressValue.value, [0, 100], [0, 0.6]);

    return {
      opacity: glowOpacity,
      width: interpolate(progressValue.value, [0, 100], [0, screenWidth]),
    };
  });

  if (!isVisible && opacityValue.value === 0) return null;

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      <View style={[styles.track, { backgroundColor: colors.borderLight }]}>
        <Animated.View
          style={[styles.glow, { backgroundColor: colors.primary }, glowStyle]}
        />
        <Animated.View
          style={[
            styles.progressBar,
            { backgroundColor: colors.primary },
            progressBarStyle,
          ]}
        />
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    height: 4,
  },
  track: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    position: 'absolute',
    left: 0,
    top: 0,
  },
  glow: {
    height: '100%',
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.3,
    shadowRadius: 8,
    shadowOpacity: 0.5,
    shadowOffset: { width: 0, height: 0 },
    elevation: 4,
  },
});
