import React, { useState } from 'react';
import { Image, ImageProps, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  Easing,
} from 'react-native-reanimated';
import StaggeredEntrance from './StaggeredEntrance';

interface AnimatedImageProps extends Omit<ImageProps, 'style'> {
  readonly style?: any;
  readonly isVisible: boolean;
  readonly delay?: number;
  readonly scaleAnimation?: boolean;
}

const AnimatedImage = Animated.createAnimatedComponent(Image);

export default function AnimatedImageComponent({
  style,
  isVisible,
  delay = 0,
  scaleAnimation = true,
  onLoad,
  ...props
}: AnimatedImageProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const imageScale = useSharedValue(0.95);
  const imageOpacity = useSharedValue(0);

  const handleImageLoad = (event: any) => {
    setImageLoaded(true);

    if (scaleAnimation) {
      imageOpacity.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      });

      imageScale.value = withSequence(
        withTiming(1.02, {
          duration: 300,
          easing: Easing.out(Easing.back(1.2)),
        }),
        withTiming(1, {
          duration: 200,
          easing: Easing.out(Easing.quad),
        }),
      );
    }

    if (onLoad) {
      onLoad(event);
    }
  };

  const imageStyle = useAnimatedStyle(() => ({
    opacity: scaleAnimation ? imageOpacity.value : 1,
    transform: scaleAnimation ? [{ scale: imageScale.value }] : [],
  }));

  if (scaleAnimation) {
    return (
      <StaggeredEntrance
        isVisible={isVisible}
        delay={delay}
        animationType="fadeUp"
      >
        <AnimatedImage
          {...props}
          style={[style, imageStyle]}
          onLoad={handleImageLoad}
        />
      </StaggeredEntrance>
    );
  }

  return (
    <StaggeredEntrance
      isVisible={isVisible}
      delay={delay}
      animationType="scale"
    >
      <Image {...props} style={style} onLoad={handleImageLoad} />
    </StaggeredEntrance>
  );
}
