import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useThemeColors } from '@/lib/store/selectors';

interface ProgressBarProps {
  readonly currentStep: number;
  readonly totalSteps: number;
  readonly stepTitles: string[];
}

export default function ProgressBar({
  currentStep,
  totalSteps,
  stepTitles,
}: ProgressBarProps) {
  const colors = useThemeColors();

  return (
    <View style={[styles.container, { backgroundColor: colors.surface }]}>
      <Text style={[styles.title, { color: colors.text }]}>
        Step {currentStep} of {totalSteps}: {stepTitles[currentStep - 1]}
      </Text>

      <View style={styles.progressContainer}>
        <View
          style={[
            styles.progressTrack,
            { backgroundColor: colors.borderLight },
          ]}
        >
          <View
            style={[
              styles.progressFill,
              {
                backgroundColor: colors.primary,
                width: `${(currentStep / totalSteps) * 100}%`,
              },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: colors.textSecondary }]}>
          {Math.round((currentStep / totalSteps) * 100)}% Complete
        </Text>
      </View>

      <View style={styles.stepsContainer}>
        {Array.from({ length: totalSteps }, (_, index) => (
          <View key={index} style={styles.stepIndicator}>
            <View
              style={[
                styles.stepCircle,
                {
                  backgroundColor:
                    index < currentStep ? colors.primary : colors.borderLight,
                  borderColor:
                    index === currentStep - 1 ? colors.primary : colors.border,
                },
              ]}
            >
              <Text
                style={[
                  styles.stepNumber,
                  {
                    color:
                      index < currentStep
                        ? colors.textInverse
                        : colors.textSecondary,
                  },
                ]}
              >
                {index + 1}
              </Text>
            </View>
            <Text
              style={[
                styles.stepLabel,
                {
                  color:
                    index === currentStep - 1
                      ? colors.primary
                      : colors.textSecondary,
                },
              ]}
            >
              {stepTitles[index]}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 16,
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressTrack: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    textAlign: 'center',
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stepIndicator: {
    alignItems: 'center',
    flex: 1,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  stepNumber: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
  },
  stepLabel: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    textAlign: 'center',
    maxWidth: 80,
  },
});
