import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Platform,
} from 'react-native';
import { Calendar, CircleAlert as AlertCircle } from 'lucide-react-native';
import { format } from 'date-fns';
import { useThemeColors } from '@/lib/store/selectors';

interface DatePickerProps {
  readonly label: string;
  readonly value: Date | null | undefined;
  readonly onChange: (date: Date) => void;
  readonly error?: string;
  readonly required?: boolean;
  readonly minimumDate?: Date;
  readonly maximumDate?: Date;
  readonly accessibilityLabel?: string;
}

export default function DatePicker({
  label,
  value,
  onChange,
  error,
  required = false,
  minimumDate,
  maximumDate,
  accessibilityLabel,
}: DatePickerProps) {
  const colors = useThemeColors();
  const [showPicker, setShowPicker] = useState(false);

  const handleDateSelect = (selectedDate: Date) => {
    onChange(selectedDate);
    setShowPicker(false);
  };

  const formatDisplayDate = (date: Date | null | undefined) => {
    if (!date) return 'Select date';
    return format(new Date(date), 'MMM dd, yyyy');
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.label, { color: colors.text }]}>
        {label}
        {required && (
          <Text style={[styles.required, { color: colors.error }]}> *</Text>
        )}
      </Text>

      <TouchableOpacity
        style={[
          styles.dateButton,
          {
            borderColor: error ? colors.error : colors.border,
            backgroundColor: colors.surface,
          },
        ]}
        onPress={() => setShowPicker(true)}
        accessibilityLabel={accessibilityLabel ?? `${label} date picker`}
        accessibilityHint="Tap to select a date"
        accessibilityRole="button"
      >
        <Text
          style={[
            styles.dateText,
            {
              color: value ? colors.text : colors.textTertiary,
            },
          ]}
        >
          {formatDisplayDate(value)}
        </Text>
        <Calendar size={20} color={colors.textSecondary} />
      </TouchableOpacity>

      {error && (
        <View style={styles.errorContainer}>
          <AlertCircle size={16} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error}
          </Text>
        </View>
      )}

      <Modal
        visible={showPicker}
        transparent
        animationType="slide"
        onRequestClose={() => setShowPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[styles.modalContent, { backgroundColor: colors.surface }]}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select {label}
              </Text>
              <TouchableOpacity
                onPress={() => setShowPicker(false)}
                style={styles.closeButton}
              >
                <Text
                  style={[styles.closeButtonText, { color: colors.primary }]}
                >
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>

            {Platform.OS === 'web' ? (
              <WebDatePicker
                value={value}
                onChange={handleDateSelect}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
              />
            ) : (
              <NativeDatePicker
                value={value}
                onChange={handleDateSelect}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
              />
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

// Web-specific date picker component
function WebDatePicker({
  value,
  onChange,
  minimumDate,
  maximumDate,
}: Readonly<{
  value: Date | null | undefined;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
}>) {
  const colors = useThemeColors();
  const handleDateChange = (dateString: string) => {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      onChange(date);
    }
  };

  return (
    <View style={styles.webDatePicker}>
      <input
        type="date"
        value={value ? format(new Date(value), 'yyyy-MM-dd') : ''}
        onChange={e => handleDateChange(e.target.value)}
        min={minimumDate ? format(minimumDate, 'yyyy-MM-dd') : undefined}
        max={maximumDate ? format(maximumDate, 'yyyy-MM-dd') : undefined}
        style={{
          width: '100%',
          padding: 12,
          fontSize: 16,
          borderRadius: 8,
          border: `1px solid ${colors.border}`,
          backgroundColor: colors.surface,
          color: colors.text,
        }}
      />
    </View>
  );
}

// Native date picker component (placeholder for actual implementation)
function NativeDatePicker({
  value,
  onChange,
  minimumDate,
  maximumDate,
}: Readonly<{
  value: Date | null | undefined;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
}>) {
  // This would use a native date picker on iOS/Android
  // For now, we'll use a simple implementation
  return (
    <View style={styles.nativeDatePicker}>
      <Text>Native date picker would go here</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
    marginBottom: 8,
  },
  required: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 14,
    minHeight: 48,
  },
  dateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  errorText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 6,
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  webDatePicker: {
    marginBottom: 20,
  },
  nativeDatePicker: {
    padding: 20,
    alignItems: 'center',
  },
});
