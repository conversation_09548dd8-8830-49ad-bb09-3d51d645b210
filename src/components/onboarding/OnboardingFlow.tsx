import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  ArrowRight,
  CircleCheck as CheckCircle,
} from 'lucide-react-native';
import { useOnboarding } from '@/lib/contexts/OnboardingContext';
import { useThemeColors } from '@/lib/store/selectors';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ProgressBar from './ProgressBar';
import PersonalInfoStep from './steps/PersonalInfoStep';
import DiseaseHistoryStep from './steps/DiseaseHistoryStep';
import MedicationsStep from './steps/MedicationsStep';
import ReviewStep from './steps/ReviewStep';

const STEP_TITLES = [
  'Personal Info',
  'Disease History',
  'Medications',
  'Review',
];

export default function OnboardingFlow() {
  const colors = useThemeColors();
  const { currentStep, previousStep, nextStep, isSubmitting } = useOnboarding();

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoStep />;
      case 2:
        return <DiseaseHistoryStep />;
      case 3:
        return <MedicationsStep />;
      case 4:
        return <ReviewStep />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ProgressBar
          currentStep={currentStep}
          totalSteps={4}
          stepTitles={STEP_TITLES}
        />

        <View style={styles.content}>{renderCurrentStep()}</View>

        <View
          style={[
            styles.navigationContainer,
            { backgroundColor: colors.surface, borderTopColor: colors.border },
          ]}
        >
          <TouchableOpacity
            style={[
              styles.navButton,
              styles.secondaryButton,
              {
                backgroundColor: colors.surface,
                borderColor: colors.border,
                opacity: currentStep === 1 ? 0.5 : 1,
              },
            ]}
            onPress={previousStep}
            disabled={currentStep === 1}
            accessibilityLabel="Previous step"
          >
            <ArrowLeft size={20} color={colors.textSecondary} />
            <Text
              style={[styles.navButtonText, { color: colors.textSecondary }]}
            >
              Previous
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.navButton,
              styles.primaryButton,
              { backgroundColor: colors.primary },
            ]}
            onPress={nextStep}
            disabled={isSubmitting}
            accessibilityLabel={currentStep === 4 ? 'Submit form' : 'Next step'}
          >
            {isSubmitting ? (
              <LoadingSpinner
                size={20}
                isVisible={true}
                color={colors.textInverse}
              />
            ) : currentStep === 4 ? (
              <CheckCircle size={20} color={colors.textInverse} />
            ) : (
              <ArrowRight size={20} color={colors.textInverse} />
            )}
            <Text style={[styles.navButtonText, { color: colors.textInverse }]}>
              {isSubmitting
                ? 'Submitting...'
                : currentStep === 4
                  ? 'Submit'
                  : 'Next'}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    justifyContent: 'center',
  },
  primaryButton: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  secondaryButton: {
    borderWidth: 1,
  },
  navButtonText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
});
