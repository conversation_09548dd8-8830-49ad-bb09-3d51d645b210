{"useTabs": false, "tabWidth": 2, "singleQuote": true, "semi": true, "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "printWidth": 80, "endOfLine": "lf", "quoteProps": "as-needed", "proseWrap": "preserve", "overrides": [{"files": ["*.json", "*.jsonc"], "options": {"singleQuote": false}}, {"files": ["*.md", "*.mdx"], "options": {"proseWrap": "always", "printWidth": 100}}]}