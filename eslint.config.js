const { defineConfig } = require("eslint/config");
const expoConfig = require("eslint-config-expo/flat");

const eslintConfig = defineConfig([
  expoConfig,
  {
    ignores: ["dist/*", "node_modules/*", ".expo/*", "ios/*", "android/*"],
  },
])

module.exports = (async function config() {
  // const { default: love } = await import("eslint-config-love");

  // const modifiedLovePlugins = Object.fromEntries(
  //   Object.entries(love.plugins).filter(([key]) => key !== "import")
  // )

  // // Remove import plugin from love config
  // const modifiedLoveConfig = {
  //   ...love,
  //   plugins: modifiedLovePlugins,
  //   files: ["**/*.js", "**/*.ts", "**/*.tsx", "**/*.jsx"],
  // }

  // eslintConfig.push(modifiedLoveConfig)

  return eslintConfig;
})();
