# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Start development server**: `bun dev` (uses <PERSON> with telemetry disabled)
- **Run on Android**: `bun run android` 
- **Run on iOS**: `bun run ios`
- **Run on web**: `bun run web`
- **Build for web**: `bun run build:web`
- **Lint code**: `bun run lint` (uses Expo's built-in linter)
- **Format code**: `bun run format` (uses Prettier)
- **Type checking**: `bun run type-check` (TypeScript validation)

## Project Architecture

This is a React Native mobile application built with Expo Router and TypeScript, designed for liver health nutrition tracking and management.

### Core Technologies

- **Runtime**: Expo SDK 53 with React Native 0.79.4
- **Package Manager**: Bun (bun.lock.b used instead of npm/yarn)
- **Navigation**: Expo Router with file-based routing
- **Language**: TypeScript with strict mode
- **State Management**: Zustand with slice-based architecture
- **Backend**: Supabase (authentication, database, real-time subscriptions)
- **Form Validation**: Zod schemas with React Hook Form
- **Animations**: React Native Reanimated 3
- **Storage**: AsyncStorage for local persistence
- **Database**: Expo SQLite for offline data + Supabase for cloud sync

### State Management Architecture

The app uses **Zustand** with a slice-based architecture, not React Context as mentioned in the old documentation. Key slices include:

- **AuthSlice**: User authentication, onboarding status, user profile
- **NutritionSlice**: Food tracking, meal planning, nutrition data
- **MedicationSlice**: Medication management, reminders, dosage tracking
- **SettingsSlice**: App preferences, notifications, user settings
- **UISlice**: Modal states, loading indicators, UI state
- **ThemeSlice**: Theme management (light/dark/system), color schemes

Store location: `lib/store/index.ts` with individual slices in `lib/store/slices/`

### File Structure

```
app/
├── _layout.tsx           # Root layout with theme and font providers
├── (tabs)/              # Tab-based navigation group
│   ├── _layout.tsx      # Tab navigation configuration
│   ├── index.tsx        # Home/Dashboard tab
│   ├── food-tracking.tsx
│   ├── meal-planning.tsx
│   ├── health-metrics.tsx
│   ├── settings.tsx
│   └── onboarding.tsx   # Hidden from tab bar
├── (auth)/              # Authentication screens
│   ├── sign-in.tsx
│   ├── sign-up.tsx
│   └── forgot-password.tsx
└── +not-found.tsx       # 404 page

lib/
├── store/               # Zustand store architecture
│   ├── index.ts         # Main store configuration
│   ├── slices/          # Individual state slices
│   ├── types.ts         # Shared type definitions
│   ├── selectors.ts     # Derived state selectors
│   └── hooks/           # Custom store hooks
├── contexts/            # React Context providers (AuthContext, OnboardingContext)
├── hooks/               # Custom React hooks
├── data/                # Static data and database utilities
└── errorHandler.ts      # Centralized error handling

components/
├── ui/                  # Reusable UI components with animations 
├── onboarding/          # Onboarding flow components
    ├── steps/           # Individual onboarding steps
    └── OnboardingFlow.tsx

types/
├── onboarding.ts        # Zod validation schemas
├── auth.ts             # Authentication types
└── database.types.ts   # Generated Supabase types
```

### Key Development Patterns

**State Management**: Use Zustand slices, not React Context for app state:
```typescript
// Access state via selectors
const { user, isAuthenticated } = useAppStore();
const { login, logout } = useAppStore();
```

**Form Validation**: All forms use Zod schemas with React Hook Form:
```typescript
import { personalInfoSchema } from '@/types/onboarding';
const { register, handleSubmit, formState: { errors } } = useForm({
  resolver: zodResolver(personalInfoSchema)
});
```

**Database Operations**: Supabase client with error handling:
```typescript
import { supabase } from '@/lib/supabase';
import { handleSupabaseError } from '@/lib/errorHandler';
```

**Styling**: React Native StyleSheet with theme-aware colors:
```typescript
const { colors } = useTheme();
// Use colors.primary, colors.background, etc.
```

### Medical Domain Context

This app handles sensitive medical data with comprehensive validation:

- **Liver conditions**: Hepatitis B/C, NAFLD, NASH, Cirrhosis, Wilson's Disease
- **Medications**: Liver-specific medications with dosage, frequency, timing requirements
- **Lab results**: ALT, AST, Bilirubin, Albumin, PT/INR, and other liver function tests
- **Disease staging**: Mild/Moderate/Severe/End-stage classifications
- **Nutrition tracking**: Liver-friendly foods, sodium/protein restrictions

### Development Notes

- **Package Management**: Use `bun` commands, not `npm` or `yarn`
- **Path Aliases**: Import using `@/` prefix (configured in tsconfig.json)
- **Error Handling**: All async operations use `handleSupabaseError()` utility
- **Type Safety**: Strict TypeScript with generated Supabase types
- **Performance**: Zustand with persistence partitioning to prevent storage bloat
- **Testing**: Basic test setup in `tests/` directory using conceptual Jest patterns
- **New Architecture**: Expo's new architecture enabled for better performance
- **Platform Support**: iOS, Android, and web platforms configured

### SonarQube Best Practices

The codebase follows SonarQube quality standards for React Native:

- **Code Duplication**: DRY principle with reusable components and utilities
- **Complexity**: Functions kept under 15 lines, classes under 20 methods
- **Security**: No hardcoded secrets, proper input validation, secure storage
- **Maintainability**: Clear naming conventions, proper TypeScript types
- **Reliability**: Comprehensive error handling, null checks, proper async/await
- **Performance**: Optimized re-renders, lazy loading, efficient state updates